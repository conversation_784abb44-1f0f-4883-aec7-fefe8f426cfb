<?php
/**
 * The template for displaying single work
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package scd
 */

$json_gallery = [];
$gallery      = get_field('obrazky');

if ( ! empty($gallery) ) {
    foreach ( $gallery as $id ) {
        $json_gallery[] = [
            'src'         => wp_get_attachment_image_url($id, 'full'),
            'description' => trim(strip_tags(get_post_meta($id, '_wp_attachment_image_alt', true))),
        ];
    }
}

$attrs      = [];
$taxonomies = [];
$meta       = [];

switch ( get_post_type() ) {
    case 'scd_dielo_smd':
        $breadcrumb = __('Zbierky', 'scd');
        $taxonomies = [
            __('Materiál', 'scd') => 'scd_material',
            __('Technika', 'scd') => 'scd_technika',
        ];
        $meta       = [
            __('Rozmery', 'scd')   => 'rozmery',
            __('Datovanie', 'scd') => 'datovanie',
        ];
        break;

    case 'scd_dielo_ncd':
        $breadcrumb = __('NCD', 'scd');
        $taxonomies = [
            __('NCD ročník', 'scd')    => 'scd_ncd_rocnik',
            __('NCD kategória', 'scd') => 'scd_ncd_kategoria',
            __('Hodnotenie', 'scd')    => 'scd_ocenenie',
        ];
        break;

    case 'scd_hra':
        $breadcrumb = __('Archív hier', 'scd');
        $taxonomies = [
            __('Platforma', 'scd')    => 'scd_platforma',
            __('Formát média', 'scd') => 'scd_format_media',
        ];
        $meta       = [
            __('Dátum vydania', 'scd')       => 'datum_vydania',
            __('Cena v dobe vydania', 'scd') => 'cena',
        ];
        if ( ! empty ($kopie = get_field('kopia_hry')) ) {
            foreach ( $kopie as $kopia ) {
                $attrs[__('Kópia hry', 'scd')] .= '<a href="' . esc_url($kopia['url']) . '">' . esc_html(
                        $kopia['filename'],
                    ) . '</a>';
            }
        }
}

foreach ( $taxonomies as $key => $attr ) {
    if ( ! empty ($value = get_the_terms(null, $attr)) ) {
        $attrs[$key] = esc_html(implode(', ', wp_list_pluck($value, 'name')));
    }
}

foreach ( $meta as $key => $attr ) {
    if ( ! empty ($value = get_field($attr)) ) {
        $attrs[$key] = esc_html($value);
    }
}

$vyrobcovia = [];

$suvisiace_query = [
    'suppress_filters' => false,
    'numberposts'      => 3,
    'post_status'      => 'publish',
    'post_type'        => get_post_type(),
    'post__not_in'     => [ get_the_ID() ],
    'meta_query'       => [
        'relation' => 'OR',
    ],
];

if ( get_post_type() !== 'scd_hra' ) {

    $predmet         = 'diela';
    $dizajneri_roly  = get_field('dizajner_rola');
    $vyrobcovia_roly = get_field('vyrobca_rola');

    if ( ! empty($dizajneri_roly) ) {
        $suvisiace_query['meta_query'][] = [
            'key'     => 'dizajner_rola_*_dizajner',
            'value'   => wp_list_pluck(wp_list_pluck($dizajneri_roly, 'dizajner'), 'ID'),
            'compare' => 'IN',
        ];
    }

    if ( ! empty($vyrobcovia_roly) ) {
        foreach ( $vyrobcovia_roly as $vyrobca_rola ) {
            if ( ! empty($vyrobca_rola['vyrobca']) ) {
                $rola                = empty($vyrobca_rola['rola']) ? __(
                    'Výrobca',
                    'scd',
                ) : $vyrobca_rola['rola']->name;
                $vyrobcovia[$rola][] = $vyrobca_rola['vyrobca'];
            }
        }

        $suvisiace_query['meta_query'][] = [
            'key'     => 'vyrobca_rola_*_vyrobca',
            'value'   => wp_list_pluck(wp_list_pluck($vyrobcovia_roly, 'vyrobca'), 'ID'),
            'compare' => 'IN',
        ];
    }
} else {

    $predmet   = 'hry';
    $autori    = get_field('autori');
    $studio    = get_field('studio');
    $vydavatel = get_field('vydavatel_distributor');

    if ( ! empty($studio) ) {
        $vyrobcovia[__('Štúdio', 'scd')] = $studio;
        $suvisiace_query['meta_query'][] = scd_get_in_array_meta_query('studio', wp_list_pluck($studio, 'ID'));
    }

    if ( ! empty($vydavatel) ) {
        $vyrobcovia[__('Vydavateľ / distribútor', 'scd')] = $vydavatel;
        $suvisiace_query['meta_query'][]                  = scd_get_in_array_meta_query(
            'vydavatel_distributor',
            wp_list_pluck($vydavatel, 'ID'),
        );
    }
}

add_filter('posts_where', 'scd_wildcard_meta_query');
$suvisiace = [ __('Súvisiace ' . $predmet, 'scd'), get_posts($suvisiace_query), __('Prejsť na ' . $predmet, 'scd') ];
remove_filter('posts_where', 'scd_wildcard_meta_query');

get_header();
?>
    <main id="primary">
        <!-- Begin Section hero-gallery-3-slider-->
        <section class="hero-section-top padding-style-2">
            <div class="container-large">
                <div class="row">
                    <div class="col-12">
                        <?php
                        if ( function_exists('yoast_breadcrumb') ) {
                            yoast_breadcrumb('<p id="breadcrumbs">', '</p>');
                        }
                        ?>
                        <!-- hidden 2024
					<ul class="breadcrumbs">
						<li class="breadcrumb__item"><a class="breadcrumb__link" 
							href="<?php echo esc_url(get_post_type_archive_link(get_post_type())); ?>"><?php
                        echo esc_html($breadcrumb); ?></a></li>
					</ul>
					-->
                    </div>
                    <?php if ( ! empty($gallery) ): ?>
                        <div class="col-12 d-none d-md-block">
                            <?php if ( count($gallery) > 1 ): ?>
                                <div class="pos-r detail-hero detail-hero--small js-slider-group">
                                    <div class="swiper slider-hero-three-columns js-slider-hero-three-columns">
                                        <div class="swiper-wrapper">
                                            <?php foreach ( $gallery as $id ): ?>
                                                <div class="swiper-slide hero--h-xs-480">
                                                    <?php echo wp_get_attachment_image(
                                                        $id,
                                                        'medium_large',
                                                        false,
                                                        [ 'class' => 'img--responsive img--full img--cover' ],
                                                    ); ?>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                    <?php if ( count($gallery) > 1 ) {
                                        get_template_part('template-parts/swiper', 'arrows');
                                    } ?>
                                </div>
                            <?php else: $single_image = $json_gallery[0]; ?>
                                <div class="pos-r detail-hero detail-hero--small js-gallery-block">
                                    <div class="hero--h-xs-480">
                                        <a class="js-open-gallery-item"
                                           href="<?php echo esc_url($single_image['src']); ?>">
                                            <img class="img--responsive img--full img--cover js-open-gallery"
                                                 data-gallery="<?php echo esc_attr(
                                                     json_encode($json_gallery, JSON_UNESCAPED_SLASHES),
                                                 ); ?>"
                                                 src="<?php echo esc_url($single_image['src']); ?>"
                                                 alt="<?php echo esc_url($single_image['description']); ?>"
                                                 loading="lazy">
                                        </a>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-12 d-md-none">
                            <div class="pos-r detail-hero detail-hero--small js-slider-group">
                                <div class="swiper js-slider-classic">
                                    <div class="swiper-wrapper">
                                        <?php foreach ( $gallery as $id ): ?>
                                            <div class="swiper-slide hero--h-xs-480">
                                                <?php echo wp_get_attachment_image(
                                                    $id,
                                                    'medium_large',
                                                    false,
                                                    [ 'class' => 'img--responsive img--full img--cover' ],
                                                ); ?>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                <?php if ( count($gallery) > 1 ) {
                                    get_template_part('template-parts/swiper', 'arrows');
                                } ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </section>
        <!-- End Section hero-gallery-3-slider-->
        <section>
            <div class="container-large">
                <div class="row no-gutters">
                    <div class="col-12 col-lg-7 margin-bottom-4 margin-bottom-xs-4 margin-bottom-lg-0">
                        <!-- Begin Section detail work info-->
                        <div class="box box--border box--size-2-5-5">
                            <div class="letter__item">
                                <svg class="icon-svg icon-a">
                                    <use xlink:href="<?php scd_asset('sprites.svg'); ?>#icon-a"></use>
                                </svg>
                            </div>
                            <div class="margin-bottom-1-5 h--margin-0">
                                <h1 class="alfa alfa-big"><?php the_title(); ?></h1>
                            </div>
                            <?php if ( ! scd_empty_deep($dizajneri_roly) ): ?>
                                <div class="row">
                                    <?php foreach ( $dizajneri_roly as $dizajner_rola ): ?>
                                        <div class="col-auto margin-bottom-1-2">
                                            <?php scd_author($dizajner_rola['dizajner'], $dizajner_rola['rola']); ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php elseif ( ! scd_empty_deep($autori) ): ?>
                                <div class="row">
                                    <?php foreach ( $autori as $autor ): ?>
                                        <div class="col-auto margin-bottom-1-2">
                                            <?php scd_author($autor); ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                            <?php if ( ! empty(get_the_tags()) ): ?>
                                <div class="margin-bottom-1-5 d-none d-lg-block">
                                    <?php get_template_part('template-parts/tags'); ?>
                                </div>
                            <?php endif; ?>
                            <div class="margin-bottom-1-5 h--margin-0">
                                <?php the_content(); ?>
                            </div>
                            <?php if ( ! scd_empty_deep($vyrobcovia) ): ?>
                                <div class="detail-list margin-bottom-2">
                                    <?php scd_roles_designers($vyrobcovia); ?>
                                </div>
                            <?php endif; ?>
                            <div class="detail-list">
                                <?php foreach ( $attrs as $description => $value ): if ( ! empty($value) ): ?>
                                    <div class="detail-list__item">
                                        <div class="detail-list__left-panel"><?php echo esc_html($description); ?>:
                                        </div>
                                        <div class="detail-list__right-panel"><?php echo $value; ?></div>
                                    </div>
                                <?php endif; endforeach; ?>
                            </div>
                        </div>
                        <!-- End Section detail work info-->
                    </div>
                    <?php if ( ! empty($gallery) ): ?>
                        <div class="col-12 col-lg-5">
                            <!-- Begin Section detail gallery work-->
                            <div class="box box--border box--size-2-5-2 box--md-offset-left js-gallery-block">
                                <div class="letter__item">
                                    <svg class="icon-svg icon-m">
                                        <use xlink:href="<?php scd_asset('sprites.svg'); ?>#icon-m"></use>
                                    </svg>
                                </div>
                                <div class="d-none">
                                    <button class="btn--absolute-ext btn btn--white-fill btn--min-width-small btn--font-size-small btn--size-small js-open-gallery"
                                            data-gallery="<?php echo esc_attr(
                                                json_encode($json_gallery, JSON_UNESCAPED_SLASHES),
                                            ); ?>">otvoriť galériu
                                    </button>
                                </div>
                                <div class="row gutter-10 gutter-sm-20">
                                    <?php foreach ( $gallery as $id ): ?>
                                        <div class="card__item card__item--three col-4 js-gallery-item">
                                            <div class="card__content">
                                                <a class="js-open-gallery-item card__image-block aspect-ratio-3-2"
                                                   href="<?php echo esc_url(
                                                       wp_get_attachment_image_url($id, 'full'),
                                                   ); ?>">
                                                    <?php echo wp_get_attachment_image(
                                                        $id,
                                                        'medium',
                                                        false,
                                                        [ 'class' => 'img--responsive img--full img--cover' ],
                                                    ); ?>
                                                </a>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <!-- End Section detail gallery work-->
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </section>
        <?php if ( $predmet === 'hry' ) {
            get_template_part(
                'template-parts/gallery',
                null,
                [ __('Články a recenzie', 'scd'), get_field('clanky_a_recenzie'), true ],
            );
        } ?>
        <?php get_template_part('template-parts/works', null, $suvisiace); ?>
    </main>
<?php
get_footer();
