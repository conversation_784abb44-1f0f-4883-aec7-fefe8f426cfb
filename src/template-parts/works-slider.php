<?php if ( ! empty($args[1]) ): ?>
    <!-- Begin Section similiar-work-->
    <section>
        <div class="container-large">
            <div class="row">
                <div class="col-12">
                    <div class="box box--border box--size-2-5-2 border-0-bottom">
                        <div class="letter__item">
                            <svg class="icon-svg icon-a">
                                <use xlink:href="<?php scd_asset('sprites.svg'); ?>#icon-a"></use>
                            </svg>
                        </div>
                        <div class="margin-bottom-1-5 h--margin-0">
                            <h2 class="alfa"><?php echo esc_html($args[0]); ?></h2>
                        </div>
                        <div class="js-slider-group">
                            <div class="swiper js-slider-works">
                                <div class="swiper-wrapper">
                                    <?php foreach ( $args[1] as $i => $dielo ): ?>
                                        <div class="swiper-slide card__item">
                                            <div class="card__content">
                                                <a class="card__image-block aspect-ratio-103"
                                                   href="<?php echo esc_url(get_permalink($dielo)); ?>">
                                                    <?php if ( ! empty(get_field('obrazky', $dielo)) ) {
                                                        echo wp_get_attachment_image(
                                                            current(get_field('obrazky', $dielo)),
                                                            'medium_large',
                                                            false,
                                                            [ 'class' => 'img--responsive img--full img--cover' ],
                                                        );
                                                    } ?>
                                                </a>
                                                <div class="card__text-block card__text-block--top">
                                                    <h3 class="beta card__title"><a
                                                                href="<?php echo esc_url(
                                                                    get_permalink($dielo),
                                                                ); ?>"><?php echo esc_html(
                                                                get_the_title($dielo),
                                                            ); ?></a></h3>
                                                    <?php if ( ! empty(
                                                    $dizajneri_roly = get_field(
                                                        'dizajner_rola',
                                                        $dielo,
                                                    )
                                                    ) ): ?>
                                                        <div class="card-info">
                                                            <?php foreach (
                                                                get_field(
                                                                    'dizajner_rola',
                                                                    $dielo,
                                                                ) as $dizajner
                                                            ):
                                                                if ( ! empty($dizajner['dizajner']) ): ?>
                                                                    <div class="card-info__item"><?php
                                                                        echo esc_html(
                                                                            get_the_title($dizajner['dizajner']),
                                                                        ); ?></div>
                                                                <?php endif; endforeach; ?>
                                                        </div>
                                                    <?php else:
                                                        $subjekty = empty(get_field('studio', $dielo))
                                                            ?
                                                            get_field('vydavatel_distributor', $dielo)
                                                            : get_field(
                                                                'studio',
                                                                $dielo,
                                                            );
                                                        if ( ! empty($subjekty) ):
                                                            ?>
                                                            <div class="card-info">
                                                                <?php foreach ( $subjekty as $subjekt ): ?>
                                                                    <div class="card-info__item"><?php echo esc_html(
                                                                            get_the_title($subjekt),
                                                                        ); ?></div>
                                                                <?php endforeach; ?>
                                                            </div>
                                                        <?php endif; endif; ?>
                                                    <?php get_template_part(
                                                        'template-parts/tags',
                                                        null,
                                                        [ 'post' => $dielo, 'class' => 'tag-list--no-margin' ],
                                                    ); ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php
                                    endforeach;
                                    $link = get_post_type_archive_link(get_post_type($dielo));
                                    if ( is_singular('scd_osobnost_dizajnu') && ! empty($typ = get_field('typ')) ) {
                                        $vyrobca_dizajner = $typ->slug === 'osobnosti-dizajnu' ? 'dizajner' : 'vyrobca';
                                        $link             = add_query_arg(
                                            [ $vyrobca_dizajner => get_the_ID(), 'post_type' => get_post_type($dielo) ],
                                            $link,
                                        );
                                    }
                                    ?>
                                </div>
                            </div>
                            <div class="swiper-arrow swiper-arrow--top-right swiper-arrow-next js-swiper-arrow-next">
                                <div class="swiper-arrow-content">
                                    <svg class="icon-svg icon-chevron-right">
                                        <use xlink:href="<?php scd_asset('sprites.svg'); ?>#icon-chevron-right"></use>
                                    </svg>
                                </div>
                            </div>
                            <div class="swiper-arrow swiper-arrow--top-right swiper-arrow-prev js-swiper-arrow-prev">
                                <div class="swiper-arrow-content">
                                    <svg class="icon-svg icon-chevron-left">
                                        <use xlink:href="<?php scd_asset('sprites.svg'); ?>#icon-chevron-left"></use>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                    <a class="btn btn--black-fill btn--responsive"
                       href="<?php echo esc_url($args[3]); ?>"><?php
                        echo $args[2]; ?></a>
                </div>
            </div>
        </div>
    </section>
    <!-- End Section similiar-work-->
<?php endif; ?>
