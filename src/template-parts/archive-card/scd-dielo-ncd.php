<?php if ( isset($args['post_id']) ) :
    $post_id = $args['post_id'];
    $ncd_term_name = '';
    $author_name = '';
    $author_id = 0;
    $image_id = 0;

    $ncd_term = get_the_terms($post_id, 'scd_ncd_kategoria');

    if ( ! empty($ncd_term) && ! is_wp_error($ncd_term) && isset($ncd_term[0]->name) ) {
        $ncd_term_name = $ncd_term[0]->name;
    }

    $designer_roles = get_field('dizajner_rola', $post_id);

    if ( ! empty($designer_roles) && is_array($designer_roles) ) {
        foreach ( $designer_roles as $role ) {
            if ( ! empty($role['dizajner']) ) {
                $author_id = $role['dizajner'];
                break;
            }
        }
    }

    if ( $author_id ) {
        $author_name = get_the_title($author_id);
    }

    $gallery = get_field('obrazky', $post_id);

    if ( ! empty($gallery) && is_array($gallery) ) {
        $image_id = current($gallery);
    }

    ?>

    <div class="card__content">
        <a class="card__image-block "
           href="<?php echo esc_url(get_permalink($post_id)); ?>">
            <?php echo wp_get_attachment_image(
                $image_id,
                'medium_large',
                '',
                [ 'class' => 'img--responsive img--full img--cover aspect-square' ],
            ); ?>
        </a>
        <div class="card__text-block !mt-4 lg:!mt-8">
            <div class="card__top-block">
                <h3 class="beta  !mb-2 lg:!mb-4 line-clamp-4">
                    <a href="<?php echo esc_url(get_permalink($post_id)); ?>"><?php echo esc_html(
                            get_the_title($post_id),
                        ); ?></a>
                </h3>
                <div class="card-info flex items-center gap-4">
                    <div class="text-[#202020] lg:text-[#3F3F3F] text-sm lg:text-base">

                        <?php if ( $author_name ) : ?>
                            <?php echo esc_html($author_name); ?>
                        <?php endif; ?>

                        <?php if ( $author_name && $ncd_term_name ) : ?>
                            <span class="text-[#202020] lg:text-[#3F3F3F] text-sm lg:text-base px-2">|</span>
                        <?php endif; ?>

                        <?php if ( $ncd_term_name ) : ?>
                            <?php echo esc_html($ncd_term_name); ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

        </div>
    </div>
<?php endif; ?>
