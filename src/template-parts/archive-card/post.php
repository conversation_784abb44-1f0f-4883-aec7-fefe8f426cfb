<?php if ( isset($args['post_id']) ) :
    $post_id = $args['post_id'];
    $tag_disable = $args['tag_disable'] ?? true;
    ?>

    <div class="card__content">
        <a class="card__image-block "
           href="<?php echo esc_url(get_permalink($post_id)); ?>">
            <?php echo get_the_post_thumbnail(
                $post_id,
                'medium_large',
                [ 'class' => 'img--responsive img--full img--cover aspect-square' ],
            ); ?>
        </a>
        <div class="card__text-block !mt-4 lg:!mt-8">
            <div class="card__top-block">
                <h3 class="beta  !mb-2 lg:!mb-4 line-clamp-4">
                    <a href="<?php echo esc_url(get_permalink($post_id)); ?>"><?php echo esc_html(
                            get_the_title($post_id),
                        ); ?></a>
                </h3>
                <div class="card-info">
                    <div class="text-[#202020] lg:text-[#3F3F3F] text-sm lg:text-base">
                        <?php echo esc_html(get_the_date(null, $post_id)); ?>
                    </div>
                </div>
            </div>
            <?php if ( ! $tag_disable ):
                get_template_part(
                    'template-parts/tags',
                    null,
                    [ 'post' => $post_id, 'class' => 'tag-list--no-margin' ],
                );
            endif; ?>

        </div>
    </div>
<?php endif; ?>
