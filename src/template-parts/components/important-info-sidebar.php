<?php

if ( isset($args['data']) ) :
    $fields = $args['data'];
    $enable_box = ! empty($fields['enable_box']) ? $fields['enable_box'] : true;

    if ( ! $enable_box ) {
        return;
    }

    if ( ! empty($fields['posts']) ) {
        $post_ids = $fields['posts'];
    } else {
        $args = [
            'post_type'      => 'post',
            'posts_per_page' => 4,
            'orderby'        => 'date',
            'order'          => 'DESC',
            'post_status'    => 'publish',
            'field'          => 'ids',
        ];

        $post_ids = get_posts($args);
    }

    $archive_link = get_post_type_archive_link('post');
    $type_block   = $fields['type_blok'] ?? 'links';
    ?>

    <section>
        <div class="container-large grid grid-cols-12 gap-y-12 md:gap-y-0">
            <div class="col-span-12 lg:col-span-7 relative order-2 lg:!order-1 items-stretch">
                <div class=" box box--border box--size-2-5-2 posts-swiper">
                    <div class="letter__item svg-white">
                        <svg class="icon-svg icon-a ">
                            <use xlink:href="<?php scd_asset('sprites.svg'); ?>#icon-a"></use>
                        </svg>
                    </div>

                    <?php if ( ! empty($fields['title_posts']) ): ?>
                        <div class="mb-8 flex flex-row justify-between  items-center gap-4">
                            <h2 class="alfa margin-bottom-0"><?php
                                echo esc_html($fields['title_posts']); ?></h2>

                            <div class="lg:hidden">
                                <?php get_template_part('template-parts/components/swiper-arrows', ''); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if ( ! empty($post_ids) && is_array($post_ids) ): ?>
                        <?php if ( wp_is_mobile() ): ?>
                            <div class="swiper">
                                <div class="swiper-wrapper">
                                    <?php foreach ( $post_ids as $post_id ): ?>
                                        <div class="swiper-slide">
                                            <?php get_template_part(
                                                'template-parts/archive-card/post',
                                                '',
                                                [
                                                    'post_id'     => $post_id,
                                                    'tag_disable' => true
                                                ],
                                            ); ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-5 md:gap-10">

                                <?php foreach ( $post_ids as $post_id ): ?>
                                    <div class="card__item">
                                        <?php get_template_part(
                                            'template-parts/archive-card/post',
                                            '',
                                            [
                                                'post_id'     => $post_id,
                                                'tag_disable' => true
                                            ],
                                        ); ?>
                                    </div>
                                <?php endforeach; ?>

                            </div>
                        <?php endif; ?>
                    <?php endif; ?>


                    <div class="hidden md:block">
                        <a href="<?php echo esc_url($archive_link); ?>"
                           class="btn btn--white-fill btn--responsive margin-top-1-5">
                            <?php echo esc_html(__('Všetky dôležité informácie', 'scd')); ?>
                        </a>
                    </div>

                </div>

                <div class=" md:hidden">
                    <a href="<?php echo esc_url($archive_link); ?>"
                       class="btn btn--black-fill btn--responsive">
                        <?php echo esc_html(__('Všetky dôležité informácie', 'scd')); ?>
                    </a>
                </div>
            </div>
            <div class="col-span-12 lg:col-span-5 order-1 lg:!order-2">
                <div class="box  border border-black md:border-s-0 h-full">
                    <div class="letter__item svg-white md:!hidden">
                        <svg class="icon-svg icon-a ">
                            <use xlink:href="<?php scd_asset('sprites.svg'); ?>#icon-a"></use>
                        </svg>
                    </div>
                    <div class="letter__item svg-white !hidden md:!flex">
                        <svg class="icon-svg icon-m ">
                            <use xlink:href="<?php scd_asset('sprites.svg'); ?>#icon-m"></use>
                        </svg>
                    </div>

                    <div class="wrap-dropdown lg:pb-14 border-b border-black ">
                        <div class="header-dropdown  flex items-center justify-between w-full lg:pt-10 !px-5 lg:!px-8 py-6 lg:pb-0">
                            <h2 class="alfa margin-bottom-0">
                                <?php echo esc_html($fields['title_deadlines'] ?? __('Termíny', 'scd')); ?>
                            </h2>

                            <div class="header-dropdown__toggle md:hidden">
                                <?php include( locate_template('assets/icons/chevron-down.svg') ); ?>
                            </div>
                        </div>
                        <div class="content-dropdown">
                            <?php if ( $type_block == 'links' && ! empty($fields['deadlines']) && is_array(
                                    $fields['deadlines'],
                                ) ): ?>
                                <?php foreach ( $fields['deadlines'] as $deadline ):
                                    $link = $deadline['link'] ?? '';
                                    $text = ' ' . $deadline['text'] ?? '';

                                    $dateFrom = ! empty($deadline['date_from']) ? DateTime::createFromFormat(
                                        'd/m/Y',
                                        $deadline['date_from'],
                                    ) : null;
                                    $dateTo   = ! empty($deadline['date_to']) ? DateTime::createFromFormat(
                                        'd/m/Y',
                                        $deadline['date_to'],
                                    ) : null;

                                    $dateOutput = '';
                                    if ( $dateFrom ) {
                                        $dateOutput .= $dateFrom->format('d. m.');
                                    }

                                    if ( $dateTo ) {
                                        $sameYear   = $dateFrom && $dateTo && $dateFrom->format(
                                                'Y',
                                            ) === $dateTo->format(
                                                'Y',
                                            );
                                        $dateOutput .= ' - ' . $dateTo->format('d. m.');
                                        if ( ! $sameYear ) {
                                            $dateOutput .= ' ' . $dateTo->format('Y');
                                        }
                                    } else if ( $dateFrom ) {
                                        $dateOutput .= ' ' . $dateFrom->format('Y');
                                    }

                                    $today   = ( new DateTime() )->format('Y-m-d');
                                    $isToday = (
                                        ( $dateFrom && $dateFrom->format('Y-m-d') === $today ) ||
                                        ( $dateTo && $dateTo->format('Y-m-d') === $today )
                                    );
                                    ?>

                                    <a <?php echo $link ? 'href="' . esc_url($link) . '"' : ''; ?>
                                            class="<?php echo( $isToday ? 'bg-black/5' : '' ) ?> inline-block w-full last:[&_div]:border-b-0">
                                        <div class="!mx-5 lg:!mx-8 font-medium border-b border-b-black/15 py-4">
                                            <?php echo esc_html($dateOutput); ?><?php echo esc_html(
                                                $deadline['text'],
                                            ); ?>
                                        </div>
                                    </a>
                                <?php endforeach; ?>
                            <?php endif; ?>

                            <?php if ( $type_block == 'wysiwyg' ) : ?>
                                <div class="wysiwyg-content !mx-5 lg:!mx-8 md:pt-8 ">
                                    <?php echo wp_kses_post($fields['description'] ?? ''); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <?php if ( ! empty($fields['registrovat_sa']) ): ?>
                        <a href="<?php echo esc_url($fields['registrovat_sa']); ?>"
                           class="btn btn--black-fill btn--responsive ">
                            <?php echo esc_html(__('registrovat_sa', 'scd')); ?>
                        </a>
                    <?php endif; ?>

                    <div class="wrap-dropdown lg:mb-10 lg:pt-10 !px-5 lg:!px-8 py-6 ">
                        <div class="header-dropdown flex items-center justify-between w-full">
                            <h2 class="alfa margin-bottom-0">
                                <?php echo esc_html($fields['title_links'] ?? __('title_links', 'scd')); ?>
                            </h2>

                            <div class="header-dropdown__toggle md:hidden">
                                <?php include( locate_template('assets/icons/chevron-down.svg') ); ?>
                            </div>
                        </div>
                        <div class="content-dropdown">
                            <div class="pt-8">
                                <?php get_template_part(
                                    'template-parts/mhp',
                                    'quick-links',
                                    [ '', $fields['links'] ],
                                ); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </section>

<?php endif ?>
