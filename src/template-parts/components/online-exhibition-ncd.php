<?php

if ( isset($args['data']) ) :
    $fields = $args['data'];
    $enable_box = ! empty($fields['enable_box']) ? $fields['enable_box'] : true;

    if ( ! $enable_box ) {
        return;
    }

    $args = [
        'post_type'      => 'scd_dielo_ncd',
        'posts_per_page' => $fields['limit'] ?? 12,
        'orderby'        => $fields['order_by'] ?? 'date',
        'order'          => $fields['order'] ?? 'DESC',
        'field'          => 'ids',
    ];

    if ( ! empty($fields['post_ids']) ) {
        $args['post__in'] = $fields['post_ids'];
        $args['orderby']  = 'post__in';
    }

    $posts = get_posts($args);

    ?>

    <section>
        <div class="container-large online-exhibition-ncd">
            <div class=" box box--border box--size-2-5-2">
                <div class="mb-8 flex flex-row justify-between  items-center gap-4">
                    <h2 class="alfa  margin-bottom-0"><?php
                        echo esc_html($fields['section_title']); ?>
                    </h2>
                    <div class="lg:hidden">
                        <?php get_template_part('template-parts/components/swiper-arrows', ''); ?>
                    </div>
                </div>

                <?php if ( count($posts) > 0 && wp_is_mobile() ) : ?>
                    <div class="swiper ">
                        <div class="swiper-wrapper">
                            <?php foreach ( $posts as $post_id ): ?>
                                <div class="swiper-slide">
                                    <?php get_template_part(
                                        'template-parts/archive-card/scd-dielo-ncd',
                                        '',
                                        [
                                            'post_id'     => $post_id,
                                            'tag_disable' => true
                                        ],
                                    ); ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php elseif ( count($posts) > 0 ) : ?>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 xl:gap-12">
                        <?php foreach ( $posts as $post_id ): ?>
                            <?php get_template_part(
                                'template-parts/archive-card/scd-dielo-ncd',
                                '',
                                [
                                    'post_id'     => $post_id,
                                    'tag_disable' => true
                                ],
                            ); ?>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>
<?php endif; ?>
