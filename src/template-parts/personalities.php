<?php if ( ! empty($args[1]) ): ?>
    <!-- Begin Section personality-->
    <section>
        <div class="container-large">
            <div class="row">
                <div class="col-12">
                    <div class="box box--border box--size-2-5-2 border-0-bottom">
                        <div class="letter__item">
                            <svg class="icon-svg icon-a">
                                <use xlink:href="<?php scd_asset('sprites.svg'); ?>#icon-a"></use>
                            </svg>
                        </div>
                        <div class="margin-bottom-1-5 h--margin-0">
                            <h2 class="alfa"><?php echo esc_html($args[0]); ?></h2>
                        </div>
                        <div class="js-slider-group h--margin-08 margin-bottom-1 margin-bottom-xs-0">
                            <div class="swiper js-slider-people">
                                <div class="swiper-wrapper">
                                    <?php foreach ( $args[1] as $i => $entity ): ?>
                                        <div class="swiper-slide">
                                            <div class="card__content">
                                                <?php
                                                $thumbnail = get_the_post_thumbnail(
                                                    $entity,
                                                    'medium',
                                                    [ 'class' => 'img--responsive img--full img--cover' ],
                                                );
                                                $title     = get_the_title($entity);
                                                $url       = esc_url(get_permalink($entity));
                                                $published = get_post_status($entity) === 'publish';
                                                if ( $published ) {
                                                    $title = '<a href="' . $url . '">' . $title . '</a>';
                                                }

                                                $subtitle = '';
                                                if ( ! empty($args[2]) ) {
                                                    if ( is_array($args[2]) ) {
                                                        $subtitle = $args[2][$i];
                                                        if ( $subtitle instanceof WP_Term ) {
                                                            $subtitle = $subtitle->name;
                                                        } else if ( $subtitle instanceof WP_Post ) {
                                                            $subtitle = get_the_title($subtitle);
                                                        }
                                                    } else if ( is_string($args[2]) ) {
                                                        $subtitle = get_field($args[2], $entity);
                                                    }
                                                }

                                                echo ( $published ? '<a' : '<span' ) . ' class="card__image-block aspect-ratio-1-1"' .
                                                     ( $published ? ' href="' . $url . '"' : '' ) . '>' . $thumbnail . ( $published ? '</a>' : '</span>' );
                                                ?>
                                                <div class="card__text-block card__text-block--top">
                                                    <div class="card__top-block">
                                                        <h2 class="beta card__title"><?php echo $title; ?></h2>
                                                        <?php if ( ! empty($subtitle) ): ?>
                                                            <div class="card-info">
                                                                <div class="card-info__item"><?php echo esc_html(
                                                                        $subtitle,
                                                                    ); ?></div>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <div class="swiper-arrow swiper-arrow--top-right swiper-arrow-next js-swiper-arrow-next">
                                <div class="swiper-arrow-content">
                                    <svg class="icon-svg icon-chevron-right">
                                        <use xlink:href="<?php scd_asset('sprites.svg'); ?>#icon-chevron-right"></use>
                                    </svg>
                                </div>
                            </div>
                            <div class="swiper-arrow swiper-arrow--top-right swiper-arrow-prev js-swiper-arrow-prev">
                                <div class="swiper-arrow-content">
                                    <svg class="icon-svg icon-chevron-left">
                                        <use xlink:href="<?php scd_asset('sprites.svg'); ?>#icon-chevron-left"></use>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div><?php if ( ! empty($args[3]) ): ?><a class="btn btn--black-fill btn--responsive"
                                                               href="<?php echo esc_url(
                                                                   $args[4],
                                                               ); ?>"><?php echo $args[3]; ?></a><?php endif; ?>
                </div>
            </div>
        </div>
    </section>
    <!-- End Section personality-->
<?php endif; ?>
