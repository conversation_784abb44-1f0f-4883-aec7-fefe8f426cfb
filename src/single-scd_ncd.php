<?php
/**
 * The template for displaying single NCD
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package scd
 */

// rozdel<PERSON> obsah, ak je vlo<PERSON>a <PERSON>ka (resp. <PERSON> blok) "Zobrazit viac"
$content             = get_extended(
    str_replace([ '<!-- wp:more -->', '<!-- /wp:more -->' ], '', get_post_field('post_content')),
);
$content['main']     = apply_filters('the_content', $content['main']);
$content['extended'] = apply_filters('the_content', $content['extended']);

$fields = get_fields();

$nova_hlavicka = $fields['nova_hlavicka'];

$odkazy = $fields['rychle_odkazy'];

$clanky = $fields['clanky'];

$porota = $fields['porota'];

$videa = $fields['videa'];

$prace = $fields['prace'];

$podujatia = $fields['podujatia'];

$archiv = get_posts(
    [
        'numberposts'  => -1,
        'post_status'  => 'publish',
        'post_type'    => 'scd_ncd',
        'post__not_in' => [ get_the_ID() ],
    ],
);

$secondary_nav = [
    'section-ncd'       => __('NCD', 'scd'),
    'section-events'    => ! empty($podujatia) ? __('Podujatia', 'scd') : false,
    'section-info'      => ! empty($clanky) ? __('Dôležité informácie', 'scd') : false,
    'section-judgement' => ! empty($porota) ? __('Porota', 'scd') : false,
    'section-video'     => ! empty($videa) && ! empty($videa[0]['video']['youtube_linka']) ? __('Videá', 'scd') : false,
    'section-work'      => ! empty($prace) ? __('Práce', 'scd') : false,
    'section-gallery'   => ! empty($fields['galeria']) ? __('Galéria', 'scd') : false,
    'section-partners'  => have_rows('partneri') ? __('Partneri', 'scd') : false,
    'section-archive'   => ! empty($archiv) ? __('Archív', 'scd') : false,
];

get_header(null, $secondary_nav);
get_template_part('template-parts/secondary_nav', 'desktop', $secondary_nav);
?>
    <!-- Begin Main content-->
    <main class="main--secondary-menu" id="primary">
        <?php

        if ( ! empty($nova_hlavicka) && $nova_hlavicka['enable_box'] ):
            echo '<div class="my-12">';
            get_template_part('template-parts/components/ncd-dynamic-header', null, $nova_hlavicka);
            echo '</div>';
        endif;

        if ( empty($nova_hlavicka) || ( ! $nova_hlavicka['enable_box'] && has_post_thumbnail() ) ):
            get_template_part('template-parts/components/hero-section-top');
        endif;

        ?>

        <?php
        if ( ! empty($fields['important_info_sidebar']) ) :
            get_template_part(
                'template-parts/components/important-info-sidebar',
                '',
                [
                    'data' => $fields['important_info_sidebar'],
                ],
            );
        endif
        ?>

        <?php
        if ( ! empty($fields['hlasovanie_verejnosti']) && $fields['hlasovanie_verejnosti']['enable_box'] ) :
            echo '<div class="my-12">';
            get_template_part('template-parts/components/public-vote', '', $fields['hlasovanie_verejnosti']);
            echo '</div>';
        endif;
        ?>

        <?php
        if ( ! empty($fields['online_exhibition_ncd']) ) :
            get_template_part(
                'template-parts/components/online-exhibition-ncd',
                '',
                [
                    'data' => $fields['online_exhibition_ncd'],
                ],
            );
        endif
        ?>

        <div class="section-scroll" id="section-ncd">
            <section>
                <div class="container-large">
                    <div class="row no-gutters">
                        <div class="col-12 col-lg-7 margin-bottom-4 margin-bottom-xs-4 margin-bottom-lg-0">
                            <!-- Begin Section detail-ncd-info-->
                            <div class="box box--border box--size-2-5-2 margin-bottom-2 margin-bottom-md-0">
                                <div class="letter__item">
                                    <svg class="icon-svg icon-a">
                                        <use xlink:href="<?php scd_asset('sprites.svg'); ?>#icon-a"></use>
                                    </svg>
                                </div>
                                <div class="margin-bottom-1-5 h--margin-0">
                                    <h1 class="alfa alfa-big"><?php the_title(); ?></h1>
                                </div>
                                <?php get_template_part('template-parts/tags'); ?>
                                <div class="js-active-block">
                                    <div class="typography p--line-2">
                                        <?php echo $content['main']; ?>
                                        <div class="hidden-content js-hidden-content">
                                            <?php echo $content['extended']; ?>
                                        </div>
                                    </div>
                                    <div class="hidden-content js-hidden-content is-active">
                                        <p><a class="txt-underline js-active-class-toggle"><?php esc_html_e(
                                                    'Zobraziť viac',
                                                    'scd',
                                                ); ?></a></p>
                                    </div>
                                    <div class="hidden-content js-hidden-content">
                                        <p><a class="txt-underline js-active-class-toggle"><?php esc_html_e(
                                                    'Zobraziť menej',
                                                    'scd',
                                                ); ?></a></p>
                                    </div>
                                </div>
                            </div>
                            <!-- End Section detail-ncd-info-->
                        </div>
                        <div class="col-12 col-lg-5">
                            <!-- Begin Section detail links-->
                            <div class="box box--border box--size-2-5-2 border-0-md-bottom box--md-offset-left margin-bottom-4 margin-bottom-xs-6 margin-bottom-md-0">
                                <div class="letter__item">
                                    <svg class="icon-svg icon-m">
                                        <use xlink:href="<?php scd_asset('sprites.svg'); ?>#icon-m"></use>
                                    </svg>
                                </div>
                                <?php get_template_part(
                                    'template-parts/mhp',
                                    'quick-links',
                                    [ __('Rýchle odkazy', 'scd'), $odkazy ],
                                ); ?>
                            </div>
                            <!-- End Section detail links-->
                            <!-- Begin Section detail-process-->
                            <div class="box box--border box--size-2-5-2 box--md-offset-left">
                                <div class="d-lg-none">
                                    <div class="letter__item">
                                        <svg class="icon-svg icon-c">
                                            <use xlink:href="<?php scd_asset('sprites.svg'); ?>#icon-c"></use>
                                        </svg>
                                    </div>
                                </div>
                                <div class="margin-bottom-1-5 h--margin-0">
                                    <h1 class="alfa"><?php esc_html_e('Termíny', 'scd'); ?></h1>
                                </div>
                                <div class="p--line-2">
                                    <?php echo $fields['ako_sa_zucastnit']; ?>
                                </div>
                            </div><?php if ( ! empty($fields['registrovat_sa']) && ! scd_is_kiosk() ): ?><a
                                class="btn btn--black-fill btn--responsive box--md-offset-left"
                                href="<?php echo esc_url($fields['registrovat_sa']); ?>"><?php esc_html_e(
                                    'Registrovať sa',
                                    'scd',
                                ); ?></a><?php endif; ?>
                            <!-- End Section detail-process-->
                        </div>
                    </div>
                </div>
            </section>
        </div>
        <div class="section-scroll" id="section-events">
            <?php get_template_part('template-parts/mhp', 'events', [ __('Podujatia', 'scd'), $podujatia ]); ?>
        </div>
        <?php if ( have_rows('partneri_logo') ): ?>
            <div class="section-scroll" id="section-partners">
                <section>
                    <div class="container-large">
                        <div class="row">
                            <div class="col-12">
                                <div class="box box--border box--size-2-5-2">
                                    <div class="letter__item">
                                        <svg class="icon-svg icon-a">
                                            <use xlink:href="<?php scd_asset('sprites.svg'); ?>#icon-a"></use>
                                        </svg>
                                    </div>
                                    <div class="margin-bottom-1-5 h--margin-0">
                                        <h2 class="alfa"><?php esc_html_e('Partneri NCD', 'scd'); ?></h2>
                                    </div>
                                    <div class="row gutter-10 gutter-sm-20" style="align-items: center">
                                        <?php while ( have_rows('partneri_logo') ): the_row(); ?>
                                            <div class="card__item card__item--five col-2">
                                                <?php
                                                $partner_html = wp_get_attachment_image(
                                                    get_sub_field('logo'),
                                                    'medium',
                                                    false,
                                                    [ 'class' => 'img--responsive' ],
                                                );
                                                $partner_link = get_sub_field('odkaz');
                                                if ( $partner_link ) {
                                                    $partner_html = '<a href="' . $partner_link . '" target="_blank">' . $partner_html . '</a>';
                                                }
                                                $partner_typ = get_sub_field('typ_partnera')
                                                ?>
                                                <div class="card__content">
                                                    <div class="typography p--line-1">
                                                        <span><strong><?php echo $partner_typ ?></strong></span>
                                                    </div><?php echo $partner_html; ?></div>
                                            </div>
                                        <?php endwhile; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        <?php endif; ?>

        <div class="section-scroll" id="section-info">
            <?php get_template_part('template-parts/mhp', 'articles', [ __('Dôležité informácie', 'scd'), $clanky ]); ?>
        </div>
        <?php if ( ! empty($porota) ): ?>
            <div class="section-scroll" id="section-judgement">
                <?php get_template_part(
                    'template-parts/personalities',
                    null,
                    [ __('Porota', 'scd'), $porota, 'krajina' ],
                ); ?>
            </div>
        <?php endif; ?>
        <?php if ( ! empty($videa) && ! empty($videa[0]['video']['youtube_linka']) ): ?>
            <div class="section-scroll" id="section-video">
                <?php get_template_part(
                    'template-parts/mhp',
                    'videos',
                    [ __('Súvisiace videá', 'scd'), $videa, $fields['youtube_kanal'] ],
                ); ?>
            </div>
        <?php endif; ?>
        <?php if ( ! empty($prace) ): ?>
            <div class="section-scroll" id="section-work">
                <?php get_template_part(
                    'template-parts/mhp',
                    'works',
                    [
                        __('Práce', 'scd'),
                        $prace,
                        __('Prejsť na ocenené práce', 'scd'),
                        $fields['prace_url']
                    ],
                ); ?>
            </div>
        <?php endif; ?>
        <div class="section-scroll" id="section-gallery">
            <?php get_template_part(
                'template-parts/gallery',
                null,
                [ __('Galéria', 'scd'), $fields['galeria'] ],
            ); ?>
        </div>
        <?php if ( have_rows('partneri') ): ?>
            <div class="section-scroll" id="section-partners">
                <section>
                    <div class="container-large">
                        <div class="row">
                            <div class="col-12">
                                <div class="box box--border box--size-2-5-2">
                                    <div class="letter__item">
                                        <svg class="icon-svg icon-a">
                                            <use xlink:href="<?php scd_asset('sprites.svg'); ?>#icon-a"></use>
                                        </svg>
                                    </div>
                                    <div class="margin-bottom-1-5 h--margin-0">
                                        <h2 class="alfa"><?php esc_html_e('Partneri', 'scd'); ?></h2>
                                    </div>
                                    <div class="row gutter-10 gutter-sm-20" style="align-items: center">
                                        <?php while ( have_rows('partneri') ): the_row(); ?>
                                            <div class="card__item card__item--five col-4 p-4 p-lg-5">
                                                <?php
                                                $partner_html = wp_get_attachment_image(
                                                    get_sub_field('logo'),
                                                    'medium',
                                                    false,
                                                    [ 'class' => 'img--responsive' ],
                                                );
                                                $partner_link = get_sub_field('odkaz');
                                                if ( $partner_link ) {
                                                    $partner_html = '<a href="' . $partner_link . '" target="_blank">' . $partner_html . '</a>';
                                                }
                                                ?>
                                                <div class="card__content"><?php echo $partner_html; ?></div>
                                            </div>
                                        <?php endwhile; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        <?php endif; ?>
        <?php if ( ! empty($archiv) ): ?>
            <div class="section-scroll" id="section-archive">
                <?php get_template_part(
                    'template-parts/mhp',
                    count($archiv) < 5 ? 'ncd-archive' : 'ncd-archive-slider',
                    $archiv,
                ); ?>
            </div>
        <?php endif; ?>
    </main>
<?php
get_footer();
